#!/usr/bin/env node

// Expo CLI wrapper to handle TypeScript files in expo-modules-core
const { spawn } = require('child_process');
const path = require('path');

// Set up TypeScript handling
process.env.NODE_OPTIONS = (process.env.NODE_OPTIONS || '') + ' --loader ts-node/esm';

// Alternative: Try to register ts-node
try {
  require('ts-node/register');
} catch (error) {
  // Ignore if ts-node is not available
}

// Get the expo CLI path
const expoBin = path.join(__dirname, 'node_modules', '.bin', 'expo');

// Forward all arguments to expo CLI
const args = process.argv.slice(2);
const expo = spawn('node', [expoBin, ...args], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_OPTIONS: '--loader ts-node/esm'
  }
});

expo.on('close', (code) => {
  process.exit(code);
});

expo.on('error', (error) => {
  console.error('Failed to start expo:', error);
  process.exit(1);
});
