{"expo": {"name": "THE Moment", "slug": "the-moment", "scheme": "themoment", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/default-images/default-logo.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/default-images/default-logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"bundleIdentifier": "com.hexacubic.themoment", "supportsTablet": true, "newArchEnabled": false, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "Camera access is required to take photos for document verification", "NSCalendarsUsageDescription": "Calendar access is required to add joined events to your calendar", "NSCalendarsFullAccessUsageDescription": "Full calendar access is required to create and manage events in your calendar"}}, "android": {"package": "com.hexacubic.themoment", "adaptiveIcon": {"foregroundImage": "./assets/default-images/default-logo.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-video", "expo-calendar"], "extra": {"router": {}, "eas": {"projectId": "98f7f685-9c49-419a-8957-acbfc0b4bce0"}}}}