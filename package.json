{"name": "the-moment", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "2.11.1", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@tanstack/react-query": "^5.76.2", "axios": "^1.9.0", "base-64": "^1.0.0", "country-list-with-dial-code-and-flag": "^5.1.0", "date-fns-tz": "^3.2.0", "expo": "53.0.13", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.8", "expo-calendar": "^14.1.4", "expo-camera": "~16.1.9", "expo-constants": "^17.1.6", "expo-crypto": "~14.1.5", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-image": "~2.3.0", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "^7.1.7", "expo-media-library": "~17.1.7", "expo-router": "~5.1.1", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-video": "~2.2.2", "ics": "^3.8.1", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-dropdown-picker": "^5.4.6", "react-native-image-viewing": "^0.2.2", "react-native-paper": "^5.14.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/base-64": "^1.0.2", "@types/node": "^22.15.21", "@types/react": "~19.0.10", "eas-cli": "^16.12.0", "typescript": "~5.8.3"}, "private": true}